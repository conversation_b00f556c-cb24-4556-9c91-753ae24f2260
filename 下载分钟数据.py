
import datetime
from xtquant import xtdata
import pandas as pd
import tushare as ts
import numpy as np
import warnings
warnings.filterwarnings("ignore")

token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed

# 数据年份
year = 2020
# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'none'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1m'

# 配置参数
ENABLE_FINANCIAL_DATA = True  # 是否获取财务数据
ENABLE_INDEX_COMPONENT = True  # 是否获取指数成分股信息
ENABLE_INDUSTRY_INFO = True   # 是否获取行业信息
ENABLE_INTRADAY_PRICES = True # 是否获取分时价格

def download_data(stock, period, year):
    """下载股票历史数据"""
    try:
        xtdata.download_history_data(stock_code = stock,
                                     period = period,
                                     start_time = str(year) + '0101',
                                     end_time = str(year) + '1231')
    except Exception as e:
        print(f'{stock} data failed: {e}')

def get_stock_name(stock_code):
    """获取股票名称"""
    try:
        info = xtdata.get_instrument_detail(stock_code)
        return info['InstrumentName'] if info else stock_code
    except:
        return stock_code

def get_index_components():
    """获取主要指数成分股信息"""
    index_dict = {}
    major_indices = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50',
        '000905.SH': '中证500',
        '000852.SH': '中证1000',
        '932000.CSI': '中证2000',
        '399006.SZ': '创业板指'
    }

    if ENABLE_INDEX_COMPONENT:
        print('获取指数成分股信息...')
        try:
            # 下载指数成分权重信息
            xtdata.download_index_weight()
            for index_code, index_name in major_indices.items():
                try:
                    components = xtdata.get_index_weight(index_code)
                    if components:
                        index_dict[index_name] = set(components.keys())
                        print(f'{index_name}成分股数量: {len(components)}')
                except Exception as e:
                    print(f'获取{index_name}成分股失败: {e}')
        except Exception as e:
            print(f'下载指数数据失败: {e}')

    return index_dict

def get_industry_info(stock_code):
    """获取行业分类信息"""
    if not ENABLE_INDUSTRY_INFO:
        return '', '', ''

    try:
        # 通过板块信息获取行业分类
        sector_list = xtdata.get_sector_list()
        sw_sectors = [s for s in sector_list if '申万' in s]

        for sector in sw_sectors:
            stocks_in_sector = xtdata.get_stock_list_in_sector(sector)
            if stock_code in stocks_in_sector:
                # 简单的行业分类解析
                if '申万一级' in sector:
                    return sector.replace('申万一级', '').strip(), '', ''
                elif '申万二级' in sector:
                    return '', sector.replace('申万二级', '').strip(), ''
                elif '申万三级' in sector:
                    return '', '', sector.replace('申万三级', '').strip()
    except:
        pass

    return '', '', ''

# 1 获取该时段内所有股票的列表
print('获取股票列表...')
trade_day = pro.trade_cal(exchange='', start_date=str(year)+'0101', end_date=str(year)+'1231')
trade_day = trade_day[trade_day['is_open'] == 1]
stock_list = []
for i, day in enumerate(trade_day['cal_date']):
    # 每隔20天取一次数据
    if i % 20 == 0:
        df = pro.daily(trade_date=day)
        stock_list += df['ts_code'].tolist()
        stock_list = list(set(stock_list))
print('获取股票列表完成')
print('股票个数: ', len(stock_list))

# 1.1 下载基础数据
if ENABLE_INDUSTRY_INFO:
    print('下载板块分类信息...')
    try:
        xtdata.download_sector_data()
        print('板块分类信息下载完成')
    except Exception as e:
        print(f'板块分类信息下载失败: {e}')

# 1.2 获取指数成分股信息
index_components = get_index_components()

# 1.3 下载财务数据
if ENABLE_FINANCIAL_DATA:
    print('下载财务数据...')
    try:
        # 下载主要财务报表数据
        financial_tables = ['Balance', 'Income', 'CashFlow', 'Pershareindex']
        xtdata.download_financial_data2(stock_list, financial_tables,
                                       start_time=str(year)+'0101',
                                       end_time=str(year)+'1231')
        print('财务数据下载完成')
    except Exception as e:
        print(f'财务数据下载失败: {e}')


# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=4)(delayed(download_data)(stock, period, year) for stock in stock_list)   
print('下载数据完成') 

# 3 整理数据，将下载的加密数据整理成DataFrame保存成本地.csv文件
print('整理数据...')
folder_path = 'data/' + period + '/' + dividend_type + '_' + str(year)
if not os.path.exists(folder_path): os.makedirs(folder_path)

def get_financial_metrics(stock_code, target_date=None):
    """获取财务指标数据"""
    financial_data = {
        '流通市值': np.nan, '总市值': np.nan, '净利润TTM': np.nan,
        '现金流TTM': np.nan, '净资产': np.nan, '总资产': np.nan,
        '总负债': np.nan, '净利润(当季)': np.nan
    }

    if not ENABLE_FINANCIAL_DATA:
        return financial_data

    try:
        # 获取财务数据
        fin_data = xtdata.get_financial_data([stock_code],
                                           ['Balance', 'Income', 'Pershareindex'],
                                           start_time=str(year)+'0101',
                                           end_time=str(year)+'1231')

        if stock_code in fin_data:
            stock_fin = fin_data[stock_code]

            # 获取最近的财务数据
            if 'Balance' in stock_fin and not stock_fin['Balance'].empty:
                balance = stock_fin['Balance'].iloc[-1]
                financial_data['净资产'] = balance.get('total_equity', np.nan)
                financial_data['总资产'] = balance.get('tot_assets', np.nan)
                financial_data['总负债'] = balance.get('tot_liab', np.nan)

            if 'Income' in stock_fin and not stock_fin['Income'].empty:
                income = stock_fin['Income'].iloc[-1]
                financial_data['净利润TTM'] = income.get('net_profit_incl_min_int_inc', np.nan)
                financial_data['净利润(当季)'] = income.get('net_profit_incl_min_int_inc', np.nan)

            if 'Pershareindex' in stock_fin and not stock_fin['Pershareindex'].empty:
                pershare = stock_fin['Pershareindex'].iloc[-1]
                # 通过每股净资产和股本计算市值（简化计算）
                bps = pershare.get('s_fa_bps', np.nan)
                if not pd.isna(bps):
                    # 这里需要获取股价来计算市值，暂时留空
                    pass
    except Exception as e:
        print(f'获取{stock_code}财务数据失败: {e}')

    return financial_data

def get_intraday_prices(stock_code, trade_date):
    """获取分时价格数据"""
    intraday_data = {'09:35收盘价': np.nan, '09:45收盘价': np.nan, '09:55收盘价': np.nan}

    if not ENABLE_INTRADAY_PRICES:
        return intraday_data

    try:
        # 获取1分钟数据来计算分时价格
        date_str = trade_date.strftime('%Y%m%d')
        minute_data = xtdata.get_local_data(
            field_list=['time', 'close'],
            stock_list=[stock_code],
            period='1m',
            start_time=date_str + '093000',
            end_time=date_str + '100000',
            dividend_type=dividend_type
        )

        if stock_code in minute_data and not minute_data[stock_code].empty:
            df_minute = minute_data[stock_code]
            df_minute['datetime'] = df_minute['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))

            # 获取特定时间点的价格
            for time_point, col_name in [('09:35', '09:35收盘价'), ('09:45', '09:45收盘价'), ('09:55', '09:55收盘价')]:
                target_time = pd.to_datetime(f"{date_str} {time_point}:00")
                closest_data = df_minute[df_minute['datetime'] <= target_time]
                if not closest_data.empty:
                    intraday_data[col_name] = closest_data.iloc[-1]['close']
    except Exception as e:
        print(f'获取{stock_code}分时数据失败: {e}')

    return intraday_data

# 主要数据处理循环
for i, stock in enumerate(stock_list):
    # 隔50个打印下进度
    if i%50 == 0: print(f'{i}/{len(stock_list)}: {stock}')

    try:
        # 获取基础行情数据
        data = xtdata.get_local_data(field_list=['time', 'open','close','high','low','volume',
                                                 'amount','settelementPrice', 'openInterest',
                                                 'preClose', 'suspendFlag'],
                                     stock_list=[stock],
                                     period=period,
                                     dividend_type=dividend_type)

        if stock not in data or data[stock].empty:
            print(f'{stock}: 无数据')
            continue

        df = data[stock].copy()
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df = df[df['datetime'] >= pd.to_datetime(str(year) + '0101')]
        df = df[df['datetime'] < pd.to_datetime(str(year+1) + '0101')]

        if df.empty:
            print(f'{stock}: 指定年份无数据')
            continue

        df.index = df['datetime']

        # 添加基础信息
        df['股票代码'] = stock
        df['股票名称'] = get_stock_name(stock)
        df['交易日期'] = df['datetime'].dt.date
        df['开盘价'] = df['open']
        df['最高价'] = df['high']
        df['最低价'] = df['low']
        df['收盘价'] = df['close']
        df['前收盘价'] = df['preClose']
        df['成交量'] = df['volume']
        df['成交额'] = df['amount']

        # 添加指数成分股信息
        for index_name, components in index_components.items():
            df[f'{index_name}成分股'] = 1 if stock in components else 0

        # 添加行业信息
        sw1, sw2, sw3 = get_industry_info(stock)
        df['新版申万一级行业名称'] = sw1
        df['新版申万二级行业名称'] = sw2
        df['新版申万三级行业名称'] = sw3

        # 为每个交易日添加财务数据和分时数据
        unique_dates = df['datetime'].dt.date.unique()
        for trade_date in unique_dates:
            date_mask = df['datetime'].dt.date == trade_date

            # 获取财务指标
            financial_metrics = get_financial_metrics(stock, trade_date)
            for metric_name, metric_value in financial_metrics.items():
                df.loc[date_mask, metric_name] = metric_value

            # 获取分时价格
            intraday_prices = get_intraday_prices(stock, pd.to_datetime(trade_date))
            for price_name, price_value in intraday_prices.items():
                df.loc[date_mask, price_name] = price_value

        # 添加资金流向数据（暂时填充NaN，需要专门的数据源）
        money_flow_columns = [
            '中户资金买入额', '中户资金卖出额', '大户资金买入额', '大户资金卖出额',
            '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额'
        ]
        for col in money_flow_columns:
            df[col] = np.nan

        # 选择最终输出的列
        output_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产',
            '总负债', '净利润(当季)'
        ] + money_flow_columns + [
            f'{idx}成分股' for idx in index_components.keys()
        ] + [
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:45收盘价', '09:55收盘价'
        ]

        # 确保所有列都存在
        for col in output_columns:
            if col not in df.columns:
                df[col] = np.nan

        df_output = df[output_columns]
        df_output.to_csv(folder_path + '/' + stock + '.csv', index=False)

    except Exception as e:
        print(f'{stock} 处理失败: {e}')
        continue

print('数据整理完成')
