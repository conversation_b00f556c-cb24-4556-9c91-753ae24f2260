import datetime
from xtquant import xtdata
import pandas as pd
import tushare as ts
import numpy as np
import warnings
warnings.filterwarnings("ignore")

token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed

# 数据年份
year = 2020
# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'none'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1m'

# 配置参数 - 测试版本简化配置
ENABLE_FINANCIAL_DATA = False  # 暂时关闭财务数据
ENABLE_INDEX_COMPONENT = True  # 是否获取指数成分股信息
ENABLE_INDUSTRY_INFO = False   # 暂时关闭行业信息
ENABLE_INTRADAY_PRICES = False # 暂时关闭分时价格

def download_data(stock, period, year):
    """下载股票历史数据"""
    try:
        xtdata.download_history_data(stock_code = stock, 
                                     period = period,
                                     start_time = str(year) + '0101',
                                     end_time = str(year) + '1231')
    except Exception as e:
        print(f'{stock} data failed: {e}')

def get_stock_name(stock_code):
    """获取股票名称"""
    try:
        info = xtdata.get_instrument_detail(stock_code)
        return info['InstrumentName'] if info else stock_code
    except:
        return stock_code

def get_index_components():
    """获取主要指数成分股信息"""
    index_dict = {}
    major_indices = {
        '000300.SH': '沪深300',
        '000016.SH': '上证50', 
        '000905.SH': '中证500',
    }
    
    if ENABLE_INDEX_COMPONENT:
        print('获取指数成分股信息...')
        try:
            # 下载指数成分权重信息
            xtdata.download_index_weight()
            for index_code, index_name in major_indices.items():
                try:
                    components = xtdata.get_index_weight(index_code)
                    if components:
                        index_dict[index_name] = set(components.keys())
                        print(f'{index_name}成分股数量: {len(components)}')
                except Exception as e:
                    print(f'获取{index_name}成分股失败: {e}')
        except Exception as e:
            print(f'下载指数数据失败: {e}')
    
    return index_dict

# 1 获取股票列表（测试版本只取前10只股票）
print('获取股票列表...')
try:
    df = pro.daily_basic(ts_code='', trade_date='20201230')
    stock_list = df['ts_code'].tolist()[:10]  # 只取前10只股票进行测试
    print('获取股票列表完成')
    print('股票个数: ', len(stock_list))
except Exception as e:
    print(f'获取股票列表失败: {e}')
    # 使用默认股票列表
    stock_list = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']

# 1.1 下载基础数据
if ENABLE_INDUSTRY_INFO:
    print('下载板块分类信息...')
    try:
        xtdata.download_sector_data()
        print('板块分类信息下载完成')
    except Exception as e:
        print(f'板块分类信息下载失败: {e}')

# 1.2 获取指数成分股信息
index_components = get_index_components()

# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=2)(delayed(download_data)(stock, period, year) for stock in stock_list)   
print('下载数据完成') 

# 3 整理数据
print('整理数据...')
folder_path = 'data/' + period + '/' + dividend_type + '_' + str(year) + '_enhanced'
if not os.path.exists(folder_path): os.makedirs(folder_path)

# 主要数据处理循环
for i, stock in enumerate(stock_list):
    print(f'{i+1}/{len(stock_list)}: 处理 {stock}')
    
    try:
        # 获取基础行情数据
        data = xtdata.get_local_data(field_list=['time', 'open','close','high','low','volume',
                                                 'amount','settelementPrice', 'openInterest',
                                                 'preClose', 'suspendFlag'],
                                     stock_list=[stock],
                                     period=period,
                                     dividend_type=dividend_type)
        
        if stock not in data or data[stock].empty:
            print(f'{stock}: 无数据')
            continue
            
        df = data[stock].copy()
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df = df[df['datetime'] >= pd.to_datetime(str(year) + '0101')]
        df = df[df['datetime'] < pd.to_datetime(str(year+1) + '0101')]
        
        if df.empty:
            print(f'{stock}: 指定年份无数据')
            continue
            
        df.index = df['datetime']
        
        # 添加基础信息
        df['股票代码'] = stock
        df['股票名称'] = get_stock_name(stock)
        df['交易日期'] = df['datetime'].dt.date
        df['开盘价'] = df['open']
        df['最高价'] = df['high'] 
        df['最低价'] = df['low']
        df['收盘价'] = df['close']
        df['前收盘价'] = df['preClose']
        df['成交量'] = df['volume']
        df['成交额'] = df['amount']
        
        # 添加指数成分股信息
        for index_name, components in index_components.items():
            df[f'{index_name}成分股'] = 1 if stock in components else 0
        
        # 添加暂时为空的字段
        placeholder_columns = [
            '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', 
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', 
            '大户资金买入额', '大户资金卖出额', '散户资金买入额', '散户资金卖出额', 
            '机构资金买入额', '机构资金卖出额', '新版申万一级行业名称', 
            '新版申万二级行业名称', '新版申万三级行业名称', '09:35收盘价', 
            '09:45收盘价', '09:55收盘价'
        ]
        
        for col in placeholder_columns:
            df[col] = np.nan
        
        # 选择最终输出的列
        output_columns = [
            '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
            '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', 
            '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', 
            '大户资金卖出额', '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额'
        ] + [f'{idx}成分股' for idx in index_components.keys()] + [
            '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称',
            '09:35收盘价', '09:45收盘价', '09:55收盘价'
        ]
        
        df_output = df[output_columns]
        df_output.to_csv(folder_path + '/' + stock + '.csv', index=False)
        print(f'{stock}: 数据保存完成，共{len(df_output)}条记录')
        
    except Exception as e:
        print(f'{stock} 处理失败: {e}')
        continue

print('数据整理完成')
print(f'数据保存路径: {folder_path}')
